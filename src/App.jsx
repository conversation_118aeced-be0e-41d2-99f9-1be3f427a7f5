import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-router-dom";
import { useState, useEffect } from "react";

import { About, Contact, TechQuotes, Hero, Navbar, Tech, Works, StarsCanvas } from "./components";
import WelcomeScreen from "./components/WelcomeScreen";

const App = () => {
  const [showWelcome, setShowWelcome] = useState(true);

  // Mobile viewport optimization and error handling
  useEffect(() => {
    // Ensure proper viewport meta tag for mobile
    const viewport = document.querySelector('meta[name="viewport"]');
    if (viewport) {
      viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
    }

    // Global error handler for Three.js BufferGeometry NaN errors
    const originalConsoleError = console.error;
    console.error = (...args) => {
      const message = args.join(' ');
      if (message.includes('BufferGeometry') && message.includes('NaN')) {
        console.warn('Three.js BufferGeometry NaN error intercepted and handled');
        return; // Suppress the error to prevent console spam
      }
      originalConsoleError.apply(console, args);
    };

    // Cleanup function to restore original console.error
    return () => {
      console.error = originalConsoleError;
    };
  }, []);

  const handleLoadingComplete = () => {
    setShowWelcome(false);
  };

  return (
    <>
      {showWelcome && <WelcomeScreen onLoadingComplete={handleLoadingComplete} />}

      {!showWelcome && (
        <BrowserRouter
          future={{
            v7_startTransition: true,
            v7_relativeSplatPath: true
          }}
        >
          <div className='relative z-0 bg-primary'>
            {/* Global starry background for all sections */}
            <StarsCanvas />

            {/* Navigation bar with highest z-index */}
            <Navbar />

            <div className='relative z-10'>
              <Hero />
            </div>
            <div className='relative z-10'>
              <About />
            </div>
            <div className='relative z-10'>
              <Tech />
            </div>
            <div className='relative z-10'>
              <Works />
            </div>
            <div className='relative z-10'>
              <TechQuotes />
            </div>
            <div className='relative z-10'>
              <Contact />
            </div>
          </div>
        </BrowserRouter>
      )}
    </>
  );
}

export default App;
