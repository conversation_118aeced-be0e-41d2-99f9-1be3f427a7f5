import { motion } from "framer-motion";

import { styles } from "../styles";
import { staggerContainer } from "../utils/motion";

const StarWrapper = (Component, idName) =>
  function HOC() {
    // Mobile-friendly viewport settings
    const isMobile = typeof window !== 'undefined' && window.innerWidth <= 768;

    return (
      <motion.section
        variants={staggerContainer()}
        initial='hidden'
        whileInView='show'
        viewport={{
          once: true,
          amount: isMobile ? 0.1 : 0.25, // Reduced threshold for mobile
          margin: isMobile ? "0px 0px -100px 0px" : "0px" // Trigger earlier on mobile
        }}
        className={`${styles.padding} max-w-7xl mx-auto relative z-0`}
      >
        <span className='hash-span' id={idName}>
          &nbsp;
        </span>

        <Component />
      </motion.section>
    );
  };

export default StarWrapper;
