import React, { Suspense, useState, useEffect } from 'react';
import { useLazyAnimation } from '../hooks/useIntersectionObserver';
import { useMobileDetection } from '../hooks/useMobileDetection';
import CanvasLoader from './Loader';

/**
 * Lazy loading wrapper for 3D Canvas components
 * Only loads the canvas when the section comes into view
 */
const LazyCanvas = ({ 
  children, 
  fallback = null, 
  loadingComponent = <CanvasLoader />,
  sectionId,
  forceLoad = false,
  className = "",
  style = {}
}) => {
  const { isMobile } = useMobileDetection();
  const { elementRef, shouldLoad, shouldPreload, isIntersecting } = useLazyAnimation({ isMobile });
  const [isLoaded, setIsLoaded] = useState(forceLoad);
  const [isPreloading, setIsPreloading] = useState(false);

  // Handle preloading logic
  useEffect(() => {
    if (shouldPreload && !isPreloading && !isLoaded) {
      setIsPreloading(true);
      
      // Add a small delay for smoother UX
      const preloadTimer = setTimeout(() => {
        setIsLoaded(true);
      }, isMobile ? 100 : 50);

      return () => clearTimeout(preloadTimer);
    }
  }, [shouldPreload, isPreloading, isLoaded, isMobile]);

  // Force load if specified
  useEffect(() => {
    if (forceLoad) {
      setIsLoaded(true);
    }
  }, [forceLoad]);

  const renderContent = () => {
    if (!isLoaded && !shouldLoad) {
      return fallback || (
        <div className="w-full h-full flex items-center justify-center bg-transparent">
          <div className="text-gray-500 text-sm">
            {isMobile ? "Scroll to load animation" : "Loading..."}
          </div>
        </div>
      );
    }

    if (isLoaded) {
      return (
        <Suspense fallback={loadingComponent}>
          {children}
        </Suspense>
      );
    }

    return loadingComponent;
  };

  return (
    <div 
      ref={elementRef}
      className={className}
      style={style}
      data-section-id={sectionId}
      data-lazy-loaded={isLoaded}
      data-intersecting={isIntersecting}
    >
      {renderContent()}
    </div>
  );
};

/**
 * Specific lazy canvas for Tech section (Ball animations)
 */
export const LazyTechCanvas = ({ children, ...props }) => {
  const { isMobile } = useMobileDetection();
  
  return (
    <LazyCanvas
      sectionId="tech"
      fallback={
        <div className="w-28 h-28 flex items-center justify-center bg-gray-800/20 rounded-full border border-gray-600/30">
          <div className="w-8 h-8 bg-gray-600/50 rounded-full animate-pulse" />
        </div>
      }
      {...props}
    >
      {children}
    </LazyCanvas>
  );
};

/**
 * Specific lazy canvas for Contact section (Earth animation)
 */
export const LazyContactCanvas = ({ children, ...props }) => {
  return (
    <LazyCanvas
      sectionId="contact"
      fallback={
        <div className="w-full h-full flex items-center justify-center bg-transparent">
          <div className="w-32 h-32 bg-blue-900/20 rounded-full border border-blue-500/30 animate-pulse" />
        </div>
      }
      {...props}
    >
      {children}
    </LazyCanvas>
  );
};

/**
 * Specific lazy canvas for About section (if any 3D elements)
 */
export const LazyAboutCanvas = ({ children, ...props }) => {
  return (
    <LazyCanvas
      sectionId="about"
      fallback={null} // About section might not need a fallback
      {...props}
    >
      {children}
    </LazyCanvas>
  );
};

/**
 * Hero section canvas - always loads immediately
 */
export const HeroCanvas = ({ children, ...props }) => {
  return (
    <LazyCanvas
      forceLoad={true} // Hero always loads immediately
      sectionId="hero"
      {...props}
    >
      {children}
    </LazyCanvas>
  );
};

export default LazyCanvas;
