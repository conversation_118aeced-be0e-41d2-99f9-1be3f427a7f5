import React, { useState, useRef, Suspense } from "react";
import { Canvas, useFrame } from "@react-three/fiber";
import { Points, PointMaterial } from "@react-three/drei";


// Mobile-safe star generation
const generateSafeStarPositions = (count, radius) => {
  const positions = new Float32Array(count * 3);

  for (let i = 0; i < count; i++) {
    // Generate safe spherical coordinates
    const theta = Math.random() * Math.PI * 2;
    const phi = Math.acos(2 * Math.random() - 1);
    const r = Math.cbrt(Math.random()) * radius;

    // Convert to cartesian with NaN protection
    const x = r * Math.sin(phi) * Math.cos(theta);
    const y = r * Math.sin(phi) * Math.sin(theta);
    const z = r * Math.cos(phi);

    // Validate and clamp values
    positions[i * 3] = (isNaN(x) || !isFinite(x)) ? (Math.random() - 0.5) * radius : Math.max(-radius, Math.min(radius, x));
    positions[i * 3 + 1] = (isNaN(y) || !isFinite(y)) ? (Math.random() - 0.5) * radius : Math.max(-radius, Math.min(radius, y));
    positions[i * 3 + 2] = (isNaN(z) || !isFinite(z)) ? (Math.random() - 0.5) * radius : Math.max(-radius, Math.min(radius, z));
  }

  return positions;
};

const Stars = (props) => {
  const ref = useRef();
  const isMobile = typeof window !== 'undefined' && window.innerWidth <= 768;
  // Keep high star count for both desktop and mobile
  const starCount = isMobile ? 5000 : 8000;

  const [sphere] = useState(() => {
    console.log('Generating stars for', isMobile ? 'mobile' : 'desktop', 'with count:', starCount);

    try {
      // Always use safe generation for better mobile compatibility
      const positions = generateSafeStarPositions(starCount, 1.5);

      // Validate the generated positions
      let validPositions = 0;
      for (let i = 0; i < positions.length; i++) {
        if (isFinite(positions[i]) && !isNaN(positions[i])) {
          validPositions++;
        }
      }

      console.log(`Generated ${validPositions}/${positions.length} valid star positions`);
      return positions;
    } catch (error) {
      console.error('Error generating star positions:', error);
      // Fallback to simple grid pattern
      const fallbackPositions = new Float32Array(starCount * 3);
      for (let i = 0; i < starCount; i++) {
        fallbackPositions[i * 3] = (Math.random() - 0.5) * 3;
        fallbackPositions[i * 3 + 1] = (Math.random() - 0.5) * 3;
        fallbackPositions[i * 3 + 2] = (Math.random() - 0.5) * 3;
      }
      return fallbackPositions;
    }
  });

  useFrame((state, delta) => {
    if (ref.current) {
      try {
        // Much slower rotation for better performance
        const deltaX = isNaN(delta) || !isFinite(delta) ? 0.016 : delta;
        const rotationX = ref.current.rotation.x - deltaX / 20;
        const rotationY = ref.current.rotation.y - deltaX / 30;

        // Validate rotation values before applying
        if (isFinite(rotationX) && !isNaN(rotationX)) {
          ref.current.rotation.x = rotationX;
        }
        if (isFinite(rotationY) && !isNaN(rotationY)) {
          ref.current.rotation.y = rotationY;
        }
      } catch (error) {
        console.warn('Error in stars animation frame:', error);
      }
    }
  });

  return (
    <group rotation={[0, 0, Math.PI / 4]}>
      <Points ref={ref} positions={sphere} stride={3} frustumCulled {...props}>
        <PointMaterial
          transparent
          color='#f272c8'
          size={isMobile ? 0.002 : 0.003} // Smaller stars on mobile
          sizeAttenuation={true}
          depthWrite={false}
        />
      </Points>
    </group>
  );
};

const StarsCanvas = () => {
  const isMobile = typeof window !== 'undefined' && window.innerWidth <= 768;
  const [webglSupported, setWebglSupported] = useState(true);
  const [canvasError, setCanvasError] = useState(false);

  // Check WebGL support
  React.useEffect(() => {
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      if (!gl) {
        console.warn('WebGL not supported, stars will use CSS fallback');
        setWebglSupported(false);
      }
    } catch (error) {
      console.warn('WebGL check failed:', error);
      setWebglSupported(false);
    }
  }, []);

  const handleCanvasError = (error) => {
    console.warn('Stars Canvas error, switching to fallback:', error);
    setCanvasError(true);
  };

  // CSS-only fallback when WebGL fails
  if (!webglSupported || canvasError) {
    return (
      <div className='w-full h-full fixed inset-0 z-[-1]' style={{
        background: `
          radial-gradient(2px 2px at 20px 30px, #f272c8, transparent),
          radial-gradient(2px 2px at 40px 70px, rgba(242, 114, 200, 0.8), transparent),
          radial-gradient(1px 1px at 90px 40px, #f272c8, transparent),
          radial-gradient(1px 1px at 130px 80px, rgba(242, 114, 200, 0.6), transparent),
          radial-gradient(2px 2px at 160px 30px, #f272c8, transparent),
          linear-gradient(180deg, #050816 0%, #151030 25%, #100d25 50%, #090325 75%, #050816 100%)
        `,
        backgroundRepeat: 'repeat',
        backgroundSize: '200px 100px, 200px 100px, 200px 100px, 200px 100px, 200px 100px, 100% 100%',
        animation: 'twinkle 4s ease-in-out infinite alternate'
      }}>
        <style>{`
          @keyframes twinkle {
            0% { opacity: 0.8; }
            100% { opacity: 1; }
          }
        `}</style>
      </div>
    );
  }

  return (
    <div className='w-full h-full fixed inset-0 z-[-1]' style={{
      background: 'linear-gradient(180deg, #050816 0%, #151030 25%, #100d25 50%, #090325 75%, #050816 100%)',
      backgroundSize: '100% 100%'
    }}>
      <Canvas
        camera={{ position: [0, 0, 1] }}
        gl={{
          antialias: false,
          powerPreference: isMobile ? "low-power" : "high-performance",
          failIfMajorPerformanceCaveat: false,
          alpha: true,
          premultipliedAlpha: false,
          stencil: false,
          depth: false,
          preserveDrawingBuffer: false
        }}
        dpr={isMobile ? [1, 1.5] : [1, 2]}
        frameloop='always'
        onCreated={({ gl }) => {
          console.log('Stars canvas created for', isMobile ? 'mobile' : 'desktop');

          try {
            if (isMobile) {
              gl.setPixelRatio(Math.min(window.devicePixelRatio, 1.5));
              gl.setClearColor(0x000000, 1);
            }

            gl.domElement.addEventListener('webglcontextlost', (event) => {
              console.warn('Stars WebGL context lost');
              event.preventDefault();
              setCanvasError(true);
            });

            gl.domElement.addEventListener('webglcontextrestored', () => {
              console.log('Stars WebGL context restored');
              setCanvasError(false);
            });
          } catch (error) {
            console.warn('Error setting up stars WebGL:', error);
            handleCanvasError(error);
          }
        }}
        onError={handleCanvasError}
      >
        <Suspense fallback={null}>
          <Stars />
        </Suspense>
      </Canvas>
    </div>
  );
};

export default StarsCanvas;
