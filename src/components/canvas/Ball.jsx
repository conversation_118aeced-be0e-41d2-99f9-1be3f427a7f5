import React, { Suspense, useState } from "react";
import { Canvas } from "@react-three/fiber";
import {
  Decal,
  Float,
  OrbitControls,
  Preload,
} from "@react-three/drei";
import * as THREE from "three";

import CanvasLoader from "../Loader";

const Ball = (props) => {
  const [decal, setDecal] = useState(null);
  const [textureError, setTextureError] = useState(false);

  // Load texture with error handling
  React.useEffect(() => {
    if (!props.imgUrl) {
      setTextureError(true);
      return;
    }

    try {
      const loader = new THREE.TextureLoader();
      loader.load(
        props.imgUrl,
        (texture) => {
          console.log('Texture loaded successfully:', props.imgUrl);
          setDecal(texture);
          setTextureError(false);
        },
        (progress) => {
          // Loading progress
        },
        (error) => {
          console.warn('Failed to load texture:', props.imgUrl, error);
          setTextureError(true);
        }
      );
    } catch (error) {
      console.warn('Error setting up texture loader:', error);
      setTextureError(true);
    }
  }, [props.imgUrl]);

  return (
    <Float speed={1.75} rotationIntensity={1} floatIntensity={2}>
      <ambientLight intensity={0.25} />
      <directionalLight position={[0, 0, 0.05]} />
      <mesh castShadow receiveShadow scale={2.75}>
        <icosahedronGeometry args={[1, 1]} />
        <meshStandardMaterial
          color='#fff8eb'
          polygonOffset
          polygonOffsetFactor={-5}
          flatShading
        />
        {decal && !textureError && (
          <Decal
            position={[0, 0, 1]}
            rotation={[2 * Math.PI, 0, 6.25]}
            scale={1}
            map={decal}
            flatShading
          />
        )}
        {textureError && (
          <Decal
            position={[0, 0, 1]}
            rotation={[2 * Math.PI, 0, 6.25]}
            scale={0.8}
            flatShading
          >
            <meshBasicMaterial color="#666666" />
          </Decal>
        )}
      </mesh>
    </Float>
  );
};

const BallCanvas = ({ icon }) => {
  const isMobile = typeof window !== 'undefined' && window.innerWidth <= 768;
  const [canvasError, setCanvasError] = useState(false);

  const handleCanvasError = (error) => {
    console.warn('Ball Canvas error:', error);
    setCanvasError(true);
  };

  // Fallback to 2D image if 3D fails
  if (canvasError) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-tertiary rounded-full">
        <img
          src={icon}
          alt="tech logo"
          className="w-16 h-16 object-contain filter brightness-90 hover:brightness-110 transition-all duration-300"
          onError={(e) => {
            e.target.style.display = 'none';
            e.target.nextSibling.style.display = 'block';
          }}
        />
        <div
          className="w-16 h-16 bg-gray-600 rounded-full flex items-center justify-center text-white text-xs hidden"
        >
          Tech
        </div>
      </div>
    );
  }

  return (
    <Canvas
      frameloop='always'
      dpr={isMobile ? [1, 1.5] : [1, 2]}
      gl={{
        preserveDrawingBuffer: true,
        antialias: !isMobile,
        powerPreference: "high-performance",
        failIfMajorPerformanceCaveat: false
      }}
      onCreated={({ gl }) => {
        console.log('Ball canvas created for tech logo');

        try {
          // Error handling for WebGL context loss
          gl.domElement.addEventListener('webglcontextlost', (event) => {
            console.warn('Ball WebGL context lost');
            event.preventDefault();
            setCanvasError(true);
          });

          gl.domElement.addEventListener('webglcontextrestored', () => {
            console.log('Ball WebGL context restored');
            setCanvasError(false);
          });
        } catch (error) {
          console.warn('Error setting up ball WebGL:', error);
          handleCanvasError(error);
        }
      }}
      onError={handleCanvasError}
    >
      <Suspense fallback={<CanvasLoader />}>
        <OrbitControls enableZoom={false} />
        <Ball imgUrl={icon} />
      </Suspense>

      <Preload all />
    </Canvas>
  );
};

export default BallCanvas;
