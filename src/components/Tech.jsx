import React from "react";

import { BallCanvas } from "./canvas";
import { LazyTechCanvas } from "./LazyCanvas";
import { SectionWrapper } from "../hoc";
import { technologies } from "../constants";
import { useMobileDetection } from "../hooks/useMobileDetection";

const Tech = () => {
  const { isMobile } = useMobileDetection();

  return (
    <div className='flex flex-row flex-wrap justify-center gap-10'>
      {technologies.map((technology) => (
        <div className='w-28 h-28' key={technology.name}>
          <LazyTechCanvas>
            <BallCanvas icon={technology.icon} />
          </LazyTechCanvas>
        </div>
      ))}
    </div>
  );
};

export default SectionWrapper(Tech, "tech");
