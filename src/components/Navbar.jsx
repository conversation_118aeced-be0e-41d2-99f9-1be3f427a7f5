import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";

import { styles } from "../styles";
import { navLinks } from "../constants";
import { menu, close } from "../assets";

const Navbar = () => {
  const [active, setActive] = useState("");
  const [toggle, setToggle] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      if (scrollTop > 100) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);

    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <nav
      className={`${
        styles.paddingX
      } w-full flex items-center py-5 fixed top-0 z-50 transition-all duration-300 ${
        scrolled ? "bg-primary backdrop-blur-sm" : "bg-transparent"
      }`}
    >
      <div className='w-full flex justify-between items-center max-w-7xl mx-auto'>
        <Link
          to='/'
          className='flex items-center gap-2'
          onClick={() => {
            setActive("");
            window.scrollTo(0, 0);
          }}
        >
          <span className="relative inline-block">
            <span className="absolute -inset-1 bg-gradient-to-r from-[#6366f1] to-[#a855f7] blur-lg opacity-20"></span>
            <span className="relative bg-gradient-to-r from-[#6366f1] to-[#a855f7] bg-clip-text text-transparent text-[18px] font-bold cursor-pointer">
              {`{} Sreeraj</>`}
            </span>
          </span>
        </Link>

        <ul className='list-none hidden sm:flex flex-row gap-10'>
          {navLinks.map((nav) => (
            <li
              key={nav.id}
              className={`${
                active === nav.title ? "text-white" : "text-secondary"
              } hover:text-white text-[18px] font-medium cursor-pointer`}
              onClick={() => setActive(nav.title)}
            >
              <a href={`#${nav.id}`}>{nav.title}</a>
            </li>
          ))}
        </ul>

        <div className='sm:hidden flex flex-1 justify-end items-center'>
          {/* Modern hamburger menu button with glass-morphism */}
          <button
            className="group relative p-2"
            onClick={() => setToggle(!toggle)}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-[#6366f1] to-[#a855f7] rounded-xl blur opacity-20 group-hover:opacity-40 transition duration-300"></div>
            <div className="relative rounded-xl bg-black/50 backdrop-blur-xl p-2 flex items-center justify-center border border-white/10 group-hover:border-white/20 transition-all duration-300">
              <img
                src={toggle ? close : menu}
                alt='menu'
                className='w-[24px] h-[24px] object-contain filter brightness-0 invert'
              />
            </div>
          </button>

          {/* Modern mobile menu with glass-morphism design */}
          <div
            className={`${
              !toggle ? "opacity-0 pointer-events-none scale-95" : "opacity-100 pointer-events-auto scale-100"
            } absolute top-20 right-0 mx-4 my-2 min-w-[200px] z-40 transition-all duration-300 ease-out`}
          >
            {/* Gradient background blur effect */}
            <div className="absolute -inset-1 bg-gradient-to-r from-[#6366f1] to-[#a855f7] rounded-2xl blur opacity-20"></div>

            {/* Main menu container */}
            <div className="relative bg-black/80 backdrop-blur-xl rounded-2xl border border-white/10 p-6 shadow-2xl">
              <ul className='list-none flex flex-col gap-3'>
                {navLinks.map((nav, index) => (
                  <li
                    key={nav.id}
                    className={`group mobile-menu-item ${toggle ? 'animate' : ''}`}
                  >
                    <a
                      href={`#${nav.id}`}
                      className={`relative block px-4 py-3 rounded-xl font-medium text-[16px] transition-all duration-300 ${
                        active === nav.title
                          ? "text-white bg-gradient-to-r from-[#6366f1]/20 to-[#a855f7]/20 border border-[#6366f1]/30"
                          : "text-gray-300 hover:text-white hover:bg-white/5"
                      }`}
                      onClick={() => {
                        setToggle(!toggle);
                        setActive(nav.title);
                      }}
                    >
                      {/* Hover gradient effect */}
                      <div className="absolute inset-0 bg-gradient-to-r from-[#6366f1]/10 to-[#a855f7]/10 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                      {/* Text with gradient for active state */}
                      <span className={`relative z-10 ${
                        active === nav.title
                          ? "bg-gradient-to-r from-[#6366f1] to-[#a855f7] bg-clip-text text-transparent font-semibold"
                          : ""
                      }`}>
                        {nav.title}
                      </span>

                      {/* Active indicator */}
                      {active === nav.title && (
                        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-gradient-to-r from-[#6366f1] to-[#a855f7] rounded-full"></div>
                      )}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
