import { useState, useEffect } from 'react';

/**
 * Custom hook for detecting mobile devices and screen sizes
 * Provides consistent mobile detection logic across the application
 */
export const useMobileDetection = () => {
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);
  const [screenSize, setScreenSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0
  });

  useEffect(() => {
    // Initial detection
    const detectDevice = () => {
      if (typeof window === 'undefined') return;

      const width = window.innerWidth;
      const height = window.innerHeight;
      
      setScreenSize({ width, height });
      setIsMobile(width <= 768);
      setIsTablet(width > 768 && width <= 1024);
    };

    // Detect on mount
    detectDevice();

    // Throttled resize handler for better performance
    let resizeTimer;
    const handleResize = () => {
      clearTimeout(resizeTimer);
      resizeTimer = setTimeout(detectDevice, 150);
    };

    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
      clearTimeout(resizeTimer);
    };
  }, []);

  return {
    isMobile,
    isTablet,
    isDesktop: !isMobile && !isTablet,
    screenSize,
    // Performance flags for different optimization levels
    shouldReduceAnimations: isMobile,
    shouldUseLowPower: isMobile,
    shouldSkipComplexEffects: isMobile && screenSize.width <= 480, // Very small screens
  };
};

/**
 * Performance configuration based on device type
 */
export const getPerformanceConfig = (isMobile, isTablet) => {
  if (isMobile) {
    return {
      // Canvas settings
      dpr: [1, 1.5],
      antialias: false,
      powerPreference: "low-power",
      shadows: false,
      
      // Animation settings
      frameSkip: 3,
      particleCount: 0.6, // 60% of desktop count
      animationSpeed: 0.5, // 50% of desktop speed
      
      // Memory settings
      preserveDrawingBuffer: false,
      stencil: false,
      depth: false,
    };
  }
  
  if (isTablet) {
    return {
      // Canvas settings
      dpr: [1, 1.8],
      antialias: true,
      powerPreference: "default",
      shadows: true,
      
      // Animation settings
      frameSkip: 2,
      particleCount: 0.8, // 80% of desktop count
      animationSpeed: 0.75, // 75% of desktop speed
      
      // Memory settings
      preserveDrawingBuffer: false,
      stencil: false,
      depth: true,
    };
  }
  
  // Desktop configuration
  return {
    // Canvas settings
    dpr: [1, 2],
    antialias: true,
    powerPreference: "high-performance",
    shadows: true,
    
    // Animation settings
    frameSkip: 1,
    particleCount: 1.0, // Full particle count
    animationSpeed: 1.0, // Full animation speed
    
    // Memory settings
    preserveDrawingBuffer: true,
    stencil: true,
    depth: true,
  };
};

export default useMobileDetection;
