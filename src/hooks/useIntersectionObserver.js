import { useState, useEffect, useRef } from 'react';

/**
 * Custom hook for intersection observer with lazy loading support
 * Optimized for mobile performance with configurable thresholds
 */
export const useIntersectionObserver = (options = {}) => {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [hasIntersected, setHasIntersected] = useState(false);
  const elementRef = useRef(null);
  const observerRef = useRef(null);

  const defaultOptions = {
    threshold: 0.1,
    rootMargin: '50px 0px -50px 0px',
    triggerOnce: true,
    ...options
  };

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    // Create observer with performance-optimized settings
    observerRef.current = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        const isCurrentlyIntersecting = entry.isIntersecting;
        
        setIsIntersecting(isCurrentlyIntersecting);
        
        if (isCurrentlyIntersecting && !hasIntersected) {
          setHasIntersected(true);
          
          // If triggerOnce is true, disconnect after first intersection
          if (defaultOptions.triggerOnce) {
            observerRef.current?.disconnect();
          }
        }
      },
      {
        threshold: defaultOptions.threshold,
        rootMargin: defaultOptions.rootMargin,
      }
    );

    observerRef.current.observe(element);

    return () => {
      observerRef.current?.disconnect();
    };
  }, [hasIntersected, defaultOptions.threshold, defaultOptions.rootMargin, defaultOptions.triggerOnce]);

  return {
    elementRef,
    isIntersecting,
    hasIntersected,
    shouldLoad: hasIntersected || isIntersecting
  };
};

/**
 * Hook specifically for lazy loading 3D animations
 * Provides mobile-optimized intersection detection
 */
export const useLazyAnimation = (options = {}) => {
  const { isMobile } = options;
  
  // More aggressive loading on mobile for better UX
  const mobileOptions = {
    threshold: isMobile ? 0.05 : 0.1,
    rootMargin: isMobile ? '100px 0px -20px 0px' : '50px 0px -50px 0px',
    triggerOnce: true
  };

  const {
    elementRef,
    isIntersecting,
    hasIntersected,
    shouldLoad
  } = useIntersectionObserver(mobileOptions);

  return {
    elementRef,
    isIntersecting,
    hasIntersected,
    shouldLoad,
    // Additional flags for different loading strategies
    shouldPreload: isIntersecting, // Start preloading when visible
    shouldRender: shouldLoad, // Actually render the animation
  };
};

/**
 * Hook for managing multiple lazy-loaded sections
 */
export const useLazySections = (sectionIds = []) => {
  const [loadedSections, setLoadedSections] = useState(new Set(['hero'])); // Hero always loads immediately
  const sectionRefs = useRef({});

  useEffect(() => {
    const observers = {};
    
    sectionIds.forEach(sectionId => {
      const element = document.getElementById(sectionId);
      if (!element) return;

      observers[sectionId] = new IntersectionObserver(
        (entries) => {
          const [entry] = entries;
          if (entry.isIntersecting) {
            setLoadedSections(prev => new Set([...prev, sectionId]));
            observers[sectionId]?.disconnect();
          }
        },
        {
          threshold: 0.1,
          rootMargin: '100px 0px -50px 0px'
        }
      );

      observers[sectionId].observe(element);
      sectionRefs.current[sectionId] = element;
    });

    return () => {
      Object.values(observers).forEach(observer => observer?.disconnect());
    };
  }, [sectionIds]);

  const isSectionLoaded = (sectionId) => loadedSections.has(sectionId);

  return {
    loadedSections,
    isSectionLoaded,
    sectionRefs: sectionRefs.current
  };
};

export default useIntersectionObserver;
