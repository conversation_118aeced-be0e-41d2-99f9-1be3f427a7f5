import logo from "./logo.png";
import backend from "./backend.png";
import creator from "./creator.png";
import mobile from "./mobile.png";
import web from "./web.png";
import github from "./github.png";
import menu from "./menu.svg";
import close from "./close.svg";

import css from "./tech/css.png";
import docker from "./tech/docker.png";
import figma from "./tech/figma.png";
import git from "./tech/git.png";
import html from "./tech/html.png";
import javascript from "./tech/javascript.png";
import mongodb from "./tech/mongodb.png";
import nodejs from "./tech/nodejs.png";
import reactjs from "./tech/reactjs.png";
import redux from "./tech/redux.png";
import tailwind from "./tech/tailwind.png";
import typescript from "./tech/typescript.png";
import threejs from "./tech/threejs.svg";

// New project images
import carrierguidance from "./projects/carrierguidance.png";
import eplq from "./projects/EPLQ.png";
import gymify from "./projects/Gymify.png";
import medconnectai from "./projects/medconnect-ai.png";
import portfolio from "./projects/portfolio.png";
import resumebuilder from "./projects/resumebuilder.png";
import turfbooking from "./projects/turfbooking.png";

export {
  logo,
  backend,
  creator,
  mobile,
  web,
  github,
  menu,
  close,
  css,
  docker,
  figma,
  git,
  html,
  javascript,
  mongodb,
  nodejs,
  reactjs,
  redux,
  tailwind,
  typescript,
  threejs,
  carrierguidance,
  eplq,
  gymify,
  medconnectai,
  portfolio,
  resumebuilder,
  turfbooking,
};
