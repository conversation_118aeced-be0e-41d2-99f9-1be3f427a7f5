/**
 * Performance testing utilities for mobile optimization
 */

export const performanceMonitor = {
  startTime: null,
  frameCount: 0,
  lastFrameTime: 0,
  fps: 0,
  memoryUsage: 0,

  start() {
    this.startTime = performance.now();
    this.frameCount = 0;
    this.lastFrameTime = this.startTime;
    
    // Monitor FPS
    const measureFPS = () => {
      const currentTime = performance.now();
      this.frameCount++;
      
      if (currentTime - this.lastFrameTime >= 1000) {
        this.fps = Math.round((this.frameCount * 1000) / (currentTime - this.lastFrameTime));
        this.frameCount = 0;
        this.lastFrameTime = currentTime;
        
        // Log performance metrics
        console.log(`FPS: ${this.fps}`);
        
        // Memory usage (if available)
        if (performance.memory) {
          this.memoryUsage = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
          console.log(`Memory: ${this.memoryUsage}MB`);
        }
      }
      
      requestAnimationFrame(measureFPS);
    };
    
    requestAnimationFrame(measureFPS);
  },

  getMetrics() {
    return {
      fps: this.fps,
      memoryUsage: this.memoryUsage,
      runtime: performance.now() - this.startTime
    };
  }
};

export const testMobileOptimizations = () => {
  console.log('=== Mobile Optimization Test ===');

  // Test mobile detection
  const isMobile = window.innerWidth <= 768;
  console.log(`Device type: ${isMobile ? 'Mobile' : 'Desktop'}`);
  console.log(`Screen size: ${window.innerWidth}x${window.innerHeight}`);

  // Test WebGL context
  const canvas = document.createElement('canvas');
  const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
  console.log(`WebGL support: ${gl ? 'Yes' : 'No'}`);

  if (gl) {
    const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
    if (debugInfo) {
      console.log(`GPU: ${gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL)}`);
    }
  }

  // Start performance monitoring
  performanceMonitor.start();

  // Test optimization implementations
  setTimeout(() => {
    console.log('=== Testing Optimizations ===');

    // Test 1: Star animation state
    const starCanvas = document.querySelector('canvas');
    if (starCanvas) {
      console.log(`✓ Star canvas found: ${starCanvas.width}x${starCanvas.height}`);
      console.log(`✓ Animation should be: ${isMobile ? 'STATIC' : 'ANIMATED'}`);
    }

    // Test 2: Lazy loading sections
    const sections = ['tech', 'contact', 'about'];
    sections.forEach(sectionId => {
      const section = document.getElementById(sectionId);
      if (section) {
        const lazyElements = section.querySelectorAll('[data-lazy-loaded]');
        console.log(`✓ Section ${sectionId}: ${lazyElements.length} lazy elements found`);
      }
    });

    // Test 3: Performance configuration
    console.log(`✓ Mobile optimizations: ${isMobile ? 'ENABLED' : 'DISABLED'}`);
    console.log(`✓ Expected particle reduction: ${isMobile ? '40%' : '0%'}`);
    console.log(`✓ Expected frame skipping: ${isMobile ? '3x' : '1x'}`);

    // Test 4: Memory usage
    if (performance.memory) {
      const memoryMB = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
      console.log(`✓ Current memory usage: ${memoryMB}MB`);
      console.log(`✓ Memory target: ${isMobile ? '<100MB' : '<200MB'}`);
    }

  }, 3000);

  return {
    isMobile,
    webglSupported: !!gl,
    screenSize: { width: window.innerWidth, height: window.innerHeight }
  };
};

// Auto-run test in development
if (import.meta.env.DEV) {
  window.testMobileOptimizations = testMobileOptimizations;
  console.log('Performance test available: window.testMobileOptimizations()');
}
